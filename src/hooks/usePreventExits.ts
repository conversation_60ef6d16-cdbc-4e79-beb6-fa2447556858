import { UsePreventExits } from '@/types/PreventExitTypes';
import { useEffect, useRef, useState } from 'react';
import { useNavigationContext } from './useNavigationContext';

/**
 * Custom hook to prevent page refresh
 * It provides functionality to not let user to refresh.
 *
 * @returns {Object} - An object containing:
 *   - `setPreventExit` (function): A function to enable or disable the prevention of page refresh.
 *   - `shouldPreventExit` (boolean): A flag indicating whether exits are currently blocked.
 */

const usePreventExits = (): UsePreventExits => {
  const [shouldPreventExit, setShouldPreventExit] = useState(false);
  const { setIsBlocked } = useNavigationContext();

  const shouldPreventRef = useRef(false);
  console.log({ shouldPreventExit, shouldPreventRef });

  useEffect(() => {
    console.log('From Effect', shouldPreventExit);
    setIsBlocked(shouldPreventExit);
    shouldPreventRef.current = shouldPreventExit;
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (shouldPreventExit) {
        event.preventDefault();
        event.returnValue = '';
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [setIsBlocked, shouldPreventExit]);

  return {
    setPreventExit: setShouldPreventExit,
    shouldPreventExit: shouldPreventRef.current,
  };
};

export default usePreventExits;
